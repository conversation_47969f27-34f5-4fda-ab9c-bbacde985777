import { createContext, useContext, useState, useEffect } from 'react'
import { COMPANY_CONFIG, SOCIAL_CONFIG } from '../../config/env'
import { useAuth } from './AuthContext'
// Import database functions
import {
  getHeroData,
  updateHeroData,
  getWhyChooseUsData,
  updateWhyChooseUsData,
  getKitchensData,
  addKitchen,
  updateKitchen,
  deleteKitchen,
  getCabinetsData,
  addCabinet,
  updateCabinet,
  deleteCabinet,
  getFooterData,
  updateFooterData,
  getCategories,
  getCompanySettings,
  updateCompanySetting
} from '../../../database/api-client.js'

const DataContext = createContext()

export const useData = () => {
  const context = useContext(DataContext)
  if (!context) {
    throw new Error('useData must be used within a DataProvider')
  }
  return context
}

export const DataProvider = ({ children }) => {
  const { user } = useAuth()

  // States for different sections
  const [heroData, setHeroData] = useState(null)
  const [whyChooseUsData, setWhyChooseUsData] = useState(null)
  const [kitchensData, setKitchensData] = useState([])
  const [cabinetsData, setCabinetsData] = useState([])
  const [footerData, setFooterData] = useState(null)
  const [categories, setCategories] = useState([])
  const [companySettings, setCompanySettings] = useState({})

  // Loading and error states
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load all data on component mount
  useEffect(() => {
    loadAllData()
  }, [])

  // Load all data from database
  const loadAllData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load all data in parallel (database should already be initialized)
      const [
        hero,
        whyChooseUs,
        kitchens,
        cabinets,
        footer,
        categoriesData,
        settings
      ] = await Promise.all([
        getHeroData(),
        getWhyChooseUsData(),
        getKitchensData(),
        getCabinetsData(),
        getFooterData(),
        getCategories(),
        getCompanySettings()
      ])

      setHeroData(hero)
      setWhyChooseUsData(whyChooseUs)
      setKitchensData(kitchens || [])
      setCabinetsData(cabinets || [])
      setFooterData(footer)
      setCategories(categoriesData || [])
      setCompanySettings(settings || {})

    } catch (err) {
      console.error('Error loading data:', err)
      setError('فشل في تحميل البيانات: ' + err.message)

      // Fallback to default data if database fails
      setHeroData({
        title: 'خبرة المطابخ - مطابخ تفوق التوقعات، وخزائن بتصاميم لا تُنسى',
        subtitle: 'نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع والحرفية العالية في خبرة المطابخ',
        background_image: 'https://readdy.ai/api/search-image?query=luxurious%20modern%20kitchen%20with%20elegant%20design%2C%20marble%20countertops%2C%20wooden%20cabinets%2C%20high-end%20appliances%2C%20soft%20lighting%2C%20spacious%20layout%2C%20minimalist%20style%2C%20professional%20photography%2C%20high%20resolution%2C%20advertisement%20quality&width=1920&height=1080&seq=1&orientation=landscape',
        primary_button_text: 'شاهد تصاميمنا',
        secondary_button_text: 'تواصل معنا'
      })

      setFooterData({
        socialMedia: [
          { platform: 'twitter', url: SOCIAL_CONFIG.twitter, icon: 'ri-twitter-line' },
          { platform: 'snapchat', url: SOCIAL_CONFIG.snapchat, icon: 'ri-snapchat-line' },
          { platform: 'instagram', url: SOCIAL_CONFIG.instagram, icon: 'ri-instagram-line' },
          { platform: 'whatsapp', url: SOCIAL_CONFIG.whatsapp, icon: 'ri-whatsapp-line' },
          { platform: 'tiktok', url: SOCIAL_CONFIG.tiktok, icon: 'ri-tiktok-line' }
        ],
        quickLinks: [
          { href: '#home', text: 'الرئيسية' },
          { href: '#why-us', text: 'لماذا نحن' },
          { href: '#kitchens', text: 'المطابخ' },
          { href: '#cabinets', text: 'الخزائن' },
          { href: '#contact', text: 'تواصل معنا' }
        ],
        contactInfo: [
          { icon: 'ri-map-pin-line', text: COMPANY_CONFIG.address },
          { icon: 'ri-phone-line', text: COMPANY_CONFIG.phone },
          { icon: 'ri-mail-line', text: COMPANY_CONFIG.email },
          { icon: 'ri-time-line', text: 'السبت - الخميس: 9 صباحاً - 9 مساءً' }
        ],
        copyright: `© 2024 ${COMPANY_CONFIG.name}. جميع الحقوق محفوظة.`
      })
    } finally {
      setLoading(false)
    }
  }

  // Hero Section Functions
  const updateHero = async (data) => {
    try {
      await updateHeroData(data, user?.id)
      setHeroData({ ...heroData, ...data })
      return { success: true }
    } catch (err) {
      console.error('Error updating hero data:', err)
      throw new Error('فشل في تحديث بيانات البانر الرئيسي')
    }
  }

  // Why Choose Us Functions
  const updateWhyChooseUs = async (data) => {
    try {
      await updateWhyChooseUsData(data, user?.id)
      setWhyChooseUsData({ ...whyChooseUsData, ...data })
      return { success: true }
    } catch (err) {
      console.error('Error updating why choose us data:', err)
      throw new Error('فشل في تحديث بيانات لماذا تختارنا')
    }
  }

  // Kitchen Functions
  const addNewKitchen = async (data) => {
    try {
      const result = await addKitchen(data, user?.id)
      // Reload kitchens data to get the updated list
      const updatedKitchens = await getKitchensData()
      setKitchensData(updatedKitchens)
      return result
    } catch (err) {
      console.error('Error adding kitchen:', err)
      throw new Error('فشل في إضافة المطبخ')
    }
  }

  const updateExistingKitchen = async (id, data) => {
    try {
      await updateKitchen(id, data, user?.id)
      // Reload kitchens data to get the updated list
      const updatedKitchens = await getKitchensData()
      setKitchensData(updatedKitchens)
      return { success: true }
    } catch (err) {
      console.error('Error updating kitchen:', err)
      throw new Error('فشل في تحديث المطبخ')
    }
  }

  const removeKitchen = async (id) => {
    try {
      await deleteKitchen(id, user?.id)
      // Remove from local state
      setKitchensData(kitchensData.filter(kitchen => kitchen.id !== id))
      return { success: true }
    } catch (err) {
      console.error('Error deleting kitchen:', err)
      throw new Error('فشل في حذف المطبخ')
    }
  }

  // Cabinet Functions
  const addNewCabinet = async (data) => {
    try {
      const result = await addCabinet(data, user?.id)
      // Reload cabinets data to get the updated list
      const updatedCabinets = await getCabinetsData()
      setCabinetsData(updatedCabinets)
      return result
    } catch (err) {
      console.error('Error adding cabinet:', err)
      throw new Error('فشل في إضافة الخزانة')
    }
  }

  const updateExistingCabinet = async (id, data) => {
    try {
      await updateCabinet(id, data, user?.id)
      // Reload cabinets data to get the updated list
      const updatedCabinets = await getCabinetsData()
      setCabinetsData(updatedCabinets)
      return { success: true }
    } catch (err) {
      console.error('Error updating cabinet:', err)
      throw new Error('فشل في تحديث الخزانة')
    }
  }

  const removeCabinet = async (id) => {
    try {
      await deleteCabinet(id, user?.id)
      // Remove from local state
      setCabinetsData(cabinetsData.filter(cabinet => cabinet.id !== id))
      return { success: true }
    } catch (err) {
      console.error('Error deleting cabinet:', err)
      throw new Error('فشل في حذف الخزانة')
    }
  }

  // Footer Functions
  const updateFooter = async (data) => {
    try {
      await updateFooterData(data, user?.id)
      setFooterData({ ...footerData, ...data })
      return { success: true }
    } catch (err) {
      console.error('Error updating footer data:', err)
      throw new Error('فشل في تحديث بيانات التذييل')
    }
  }

  // Company Settings Functions
  const updateSetting = async (key, value) => {
    try {
      await updateCompanySetting(key, value, user?.id)
      setCompanySettings({ ...companySettings, [key]: value })
      return { success: true }
    } catch (err) {
      console.error('Error updating company setting:', err)
      throw new Error('فشل في تحديث إعدادات الشركة')
    }
  }

  // Utility Functions
  const refreshData = async () => {
    await loadAllData()
  }

  const resetError = () => {
    setError(null)
  }

  // Context value
  const value = {
    // Data
    heroData,
    whyChooseUsData,
    kitchensData,
    cabinetsData,
    footerData,
    categories,
    companySettings,

    // States
    loading,
    error,

    // Hero functions
    updateHero,
    setHeroData, // Keep for backward compatibility

    // Why Choose Us functions
    updateWhyChooseUs,
    setWhyChooseUsData, // Keep for backward compatibility

    // Kitchen functions
    addNewKitchen,
    updateExistingKitchen,
    removeKitchen,
    setKitchensData, // Keep for backward compatibility

    // Cabinet functions
    addNewCabinet,
    updateExistingCabinet,
    removeCabinet,
    setCabinetsData, // Keep for backward compatibility

    // Footer functions
    updateFooter,
    setFooterData, // Keep for backward compatibility

    // Settings functions
    updateSetting,

    // Utility functions
    refreshData,
    refreshKitchens: async () => {
      const kitchens = await getKitchensData()
      setKitchensData(kitchens || [])
    },
    refreshCabinets: async () => {
      const cabinets = await getCabinetsData()
      setCabinetsData(cabinets || [])
    },
    resetError
  }

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  )
}

export { DataContext }
